body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 清除默认样式 */
ul, ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

a {
  text-decoration: none;
  color: inherit;
}

a:hover {
  text-decoration: none;
}

/* 全局字体设置 */
.ant-typography {
  color: #333;
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
}

.ant-input, .ant-select-selector {
  border-radius: 6px;
}

.ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 卡片内边距调整 */
.ant-card-body {
  padding: 20px;
}

/* 表格样式优化 */
.ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  color: #333;
}

/* 模态框样式 */
.ant-modal-content {
  border-radius: 8px;
  overflow: hidden;
}

.ant-modal-header {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-title {
  font-weight: 600;
}

/* 消息提示样式 */
.ant-message {
  z-index: 9999;
}

/* 加载动画 */
.ant-spin-dot {
  font-size: 20px;
}

/* 标签页样式 */
.ant-tabs-tab {
  padding: 12px 16px;
}

/* 分页器样式 */
.ant-pagination-item {
  border-radius: 4px;
}

.ant-pagination-item-active {
  border-color: #1890ff;
}

/* 下拉菜单样式 */
.ant-dropdown-menu {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 工具提示样式 */
.ant-tooltip-inner {
  border-radius: 4px;
}

/* 进度条样式 */
.ant-progress-bg {
  border-radius: 4px;
}

/* 开关样式 */
.ant-switch {
  border-radius: 12px;
}

/* 标签输入框样式 */
.ant-select-multiple .ant-select-selection-item {
  border-radius: 4px;
}

/* 日期选择器样式 */
.ant-picker {
  border-radius: 6px;
}

/* 上传组件样式 */
.ant-upload-drag {
  border-radius: 6px;
}

/* 步骤条样式 */
.ant-steps-item-process .ant-steps-item-icon {
  background: #1890ff;
}

/* 抽屉样式 */
.ant-drawer-content {
  border-radius: 8px 0 0 8px;
}

/* 警告框样式 */
.ant-alert {
  border-radius: 6px;
}

/* 面包屑样式 */
.ant-breadcrumb {
  margin-bottom: 16px;
}

/* 时间线样式 */
.ant-timeline-item-head {
  border-radius: 50%;
}

/* 评分样式 */
.ant-rate-star {
  margin-right: 4px;
}

/* 锚点样式 */
.ant-anchor-wrapper {
  background: #fff;
  border-radius: 6px;
  padding: 16px;
}

/* 回到顶部样式 */
.ant-back-top {
  right: 50px;
  bottom: 50px;
}

/* 空状态样式 */
.ant-empty {
  margin: 40px 0;
}

/* 结果页样式 */
.ant-result {
  padding: 48px 32px;
} 