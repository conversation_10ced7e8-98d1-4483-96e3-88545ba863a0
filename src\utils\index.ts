// 格式化日期
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
};

// 延迟函数
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// 生成随机ID
export const generateId = (): number => {
  return Date.now() + Math.floor(Math.random() * 1000);
};

// 验证邮箱格式
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// 验证手机号格式
export const isValidPhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

// 获取状态标签颜色
export const getStatusColor = (status: 'active' | 'inactive'): string => {
  return status === 'active' ? 'green' : 'red';
};

// 获取状态文本
export const getStatusText = (status: 'active' | 'inactive'): string => {
  return status === 'active' ? '启用' : '禁用';
};

// 复制到剪贴板
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('复制失败:', error);
    return false;
  }
};

// 下载文件
export const downloadFile = (data: any, filename: string, type: string = 'application/json'): void => {
  const blob = new Blob([typeof data === 'string' ? data : JSON.stringify(data, null, 2)], { type });
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

// 模拟数据生成器
export const generateMockUsers = (count: number) => {
  const users = [];
  const roles = ['admin', 'user', 'manager', 'editor'];
  const statuses = ['active', 'inactive'];
  
  for (let i = 1; i <= count; i++) {
    users.push({
      id: i,
      username: `user${i}`,
      email: `user${i}@example.com`,
      phone: `138${('00000000' + Math.floor(Math.random() * 100000000)).slice(-8)}`,
      role: roles[Math.floor(Math.random() * roles.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)] as 'active' | 'inactive',
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000)).toISOString(),
      updatedAt: new Date().toISOString(),
    });
  }
  
  return users;
};

export const generateMockPermissions = (count: number) => {
  const permissions = [];
  const modules = ['用户管理', '权限管理', '系统管理', '内容管理'];
  const actions = ['查看', '创建', '编辑', '删除'];
  
  for (let i = 1; i <= count; i++) {
    const module = modules[Math.floor(Math.random() * modules.length)];
    const action = actions[Math.floor(Math.random() * actions.length)];
    
    permissions.push({
      id: i,
      name: `${module}-${action}`,
      code: `${module.toLowerCase().replace(/管理/g, '')}_${action.toLowerCase()}`,
      description: `${module}的${action}权限`,
      module,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000)).toISOString(),
      updatedAt: new Date().toISOString(),
    });
  }
  
  return permissions;
};

export const generateMockRoles = (count: number) => {
  const roles = [];
  const roleNames = ['超级管理员', '管理员', '编辑员', '普通用户'];
  
  for (let i = 1; i <= count; i++) {
    roles.push({
      id: i,
      name: roleNames[i - 1] || `角色${i}`,
      code: `role_${i}`,
      description: `${roleNames[i - 1] || `角色${i}`}的描述`,
      permissions: generateMockPermissions(Math.floor(Math.random() * 10) + 1),
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000)).toISOString(),
      updatedAt: new Date().toISOString(),
    });
  }
  
  return roles;
};

export const generateMockSystemConfigs = (count: number) => {
  const configs = [];
  const configTypes = ['string', 'number', 'boolean', 'json'] as const;
  const configKeys = [
    'site_name', 'site_description', 'max_upload_size', 'enable_registration',
    'email_host', 'email_port', 'cache_ttl', 'session_timeout'
  ];
  
  for (let i = 1; i <= count; i++) {
    const key = configKeys[i - 1] || `config_key_${i}`;
    const type = configTypes[Math.floor(Math.random() * configTypes.length)];
    let value = '';
    
    switch (type) {
      case 'string':
        value = `配置值${i}`;
        break;
      case 'number':
        value = String(Math.floor(Math.random() * 1000));
        break;
      case 'boolean':
        value = Math.random() > 0.5 ? 'true' : 'false';
        break;
      case 'json':
        value = JSON.stringify({ key: `value${i}`, enabled: true });
        break;
    }
    
    configs.push({
      id: i,
      key,
      value,
      description: `${key}的配置说明`,
      type,
      createdAt: new Date(Date.now() - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000)).toISOString(),
      updatedAt: new Date().toISOString(),
    });
  }
  
  return configs;
}; 