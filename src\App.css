.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 自定义样式 */
.demo-logo-vertical {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

/* 表格行悬停效果 */
.ant-table-tbody > tr:hover > td {
  background: #f5f5f5 !important;
}

/* 卡片阴影效果 */
.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

/* 按钮组间距 */
.ant-btn + .ant-btn {
  margin-left: 8px;
}

/* 搜索框样式 */
.ant-input-affix-wrapper {
  border-radius: 6px;
}

/* 模态框样式 */
.ant-modal {
  border-radius: 8px;
}

.ant-modal-header {
  border-radius: 8px 8px 0 0;
}

/* 表格分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

/* 标签样式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
}

/* 统计卡片样式 */
.ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: bold;
}

/* 进度条样式 */
.ant-progress-line {
  margin-bottom: 8px;
}

/* 穿梭框样式 */
.ant-transfer {
  width: 100%;
}

.ant-transfer-list {
  width: calc(50% - 16px);
  height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-col-8 {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .ant-col-16 {
    width: 100%;
    text-align: left !important;
  }
  
  .ant-table {
    font-size: 12px;
  }
  
  .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
  }
} 