# 后台管理系统

一个基于 React + TypeScript + Vite + Ant Design + Zustand + Fetch + React Router 的现代化后台管理系统。

## 技术栈

- **React 18** - 前端框架
- **TypeScript 5** - 类型安全
- **Vite 7.0** - 构建工具
- **Ant Design 5** - UI 组件库
- **Zustand** - 状态管理
- **React Router 6** - 路由管理
- **Fetch API** - HTTP 请求
- **Pnpm** - 包管理器

## 功能特性

### 🏠 仪表盘
- 数据统计展示
- 系统健康监控
- 最近用户活动
- 快速操作入口

### 👥 用户管理
- 用户列表查看
- 添加/编辑/删除用户
- 用户状态管理
- 搜索和分页功能

### 🛡️ 角色管理
- 角色列表管理
- 权限分配
- 角色创建和编辑
- 权限穿梭框选择

### 🔐 权限管理
- 权限列表展示
- 权限分类管理
- 权限代码规范
- 模块化权限设计

### ⚙️ 系统管理
- 系统配置管理
- 配置类型支持（字符串、数字、布尔值、JSON）
- 配置项搜索
- 实时配置更新

## 项目结构

```
c-system/
├── public/             # 静态资源
│   ├── index.html     # HTML 模板
│   └── vite.svg       # Vite 图标
├── src/               # 源代码
│   ├── components/    # 公共组件
│   │   └── Layout.tsx # 主布局组件
│   ├── pages/         # 页面组件
│   │   ├── Dashboard.tsx   # 仪表盘
│   │   ├── Users.tsx       # 用户管理
│   │   ├── Roles.tsx       # 角色管理
│   │   ├── Permissions.tsx # 权限管理
│   │   └── System.tsx      # 系统管理
│   ├── services/      # API 服务
│   │   └── api.ts     # API 请求封装
│   ├── store/         # 状态管理
│   │   └── index.ts   # Zustand 状态定义
│   ├── types/         # 类型定义
│   │   └── index.ts   # TypeScript 类型
│   ├── utils/         # 工具函数
│   │   └── index.ts   # 通用工具函数
│   ├── App.tsx        # 主应用组件
│   ├── App.css        # 应用样式
│   ├── main.tsx       # 应用入口
│   └── index.css      # 全局样式
├── .eslintrc.cjs      # ESLint 配置
├── .gitignore         # Git 忽略文件
├── package.json       # 项目配置
├── tsconfig.json      # TypeScript 配置
├── tsconfig.node.json # Node.js TypeScript 配置
└── vite.config.ts     # Vite 配置
```

## 安装和运行

### 环境要求
- Node.js >= 18.0.0
- Pnpm >= 8.0.0

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本
```bash
pnpm build
```

### 预览生产构建
```bash
pnpm preview
```

### 代码检查
```bash
pnpm lint
```

## 主要依赖

```json
{
  "react": "^18.2.0",
  "react-dom": "^18.2.0",
  "react-router-dom": "^6.20.1",
  "antd": "^5.12.8",
  "zustand": "^4.4.7",
  "@ant-design/icons": "^5.2.6",
  "typescript": "^5.2.2",
  "vite": "^7.0.0"
}
```

## 功能说明

### 用户管理
- ✅ 用户列表展示
- ✅ 添加新用户
- ✅ 编辑用户信息
- ✅ 删除用户
- ✅ 用户状态切换
- ✅ 搜索功能
- ✅ 分页显示

### 角色管理
- ✅ 角色列表管理
- ✅ 创建新角色
- ✅ 编辑角色信息
- ✅ 删除角色
- ✅ 权限分配（穿梭框）
- ✅ 角色搜索

### 权限管理
- ✅ 权限列表展示
- ✅ 添加新权限
- ✅ 编辑权限信息
- ✅ 删除权限
- ✅ 模块分类
- ✅ 权限搜索

### 系统管理
- ✅ 配置项管理
- ✅ 多种数据类型支持
- ✅ 配置搜索
- ✅ 实时编辑

## 特色功能

### 🎨 现代化UI设计
- 采用 Ant Design 5 最新组件
- 响应式布局设计
- 深色/浅色主题支持
- 优雅的动画效果

### 🚀 高性能架构
- TypeScript 类型安全
- Vite 7.0 极速构建
- Zustand 轻量级状态管理
- 组件懒加载
- 代码分割优化

### 🔧 开发体验
- 完整的 TypeScript 类型定义
- ESLint 代码规范
- 热重载开发
- 模块化架构
- 路径别名支持 (@/ 映射到 src/)

### 📱 响应式设计
- 移动端适配
- 平板端优化
- 桌面端完整体验

## 数据模拟

当前版本使用模拟数据进行演示，包括：
- 用户数据生成
- 角色权限数据
- 系统配置数据
- 统计数据模拟

## 后续计划

- [ ] 集成真实后端 API
- [ ] 添加用户认证
- [ ] 实现文件上传功能
- [ ] 添加数据导出功能
- [ ] 集成图表组件
- [ ] 添加国际化支持
- [ ] 实现主题切换
- [ ] 添加更多页面模板
- [ ] PWA 支持
- [ ] 单元测试覆盖

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建 Issue
- 发送邮件

---

⭐ 如果这个项目对你有帮助，请给它一个星标！ 