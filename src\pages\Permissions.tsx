import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  Popconfirm,
  message,
  Card,
  Row,
  Col,
  Typography,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { Permission } from '../types';
import { generateMockPermissions, formatDate } from '../utils';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const Permissions: React.FC = () => {
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);
  const [searchText, setSearchText] = useState('');
  const [form] = Form.useForm();

  useEffect(() => {
    loadPermissions();
  }, []);

  const loadPermissions = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const mockPermissions = generateMockPermissions(30);
      setPermissions(mockPermissions);
    } catch (error) {
      message.error('加载权限数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingPermission(null);
    setModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (permission: Permission) => {
    setEditingPermission(permission);
    setModalVisible(true);
    form.setFieldsValue(permission);
  };

  const handleDelete = async (id: number) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      setPermissions(permissions.filter(permission => permission.id !== id));
      message.success('删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingPermission) {
        const updatedPermission = {
          ...editingPermission,
          ...values,
          updatedAt: new Date().toISOString(),
        };
        setPermissions(permissions.map(permission => 
          permission.id === editingPermission.id ? updatedPermission : permission
        ));
        message.success('更新成功');
      } else {
        const newPermission: Permission = {
          id: Date.now(),
          ...values,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        setPermissions([...permissions, newPermission]);
        message.success('添加成功');
      }
      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const filteredPermissions = permissions.filter(permission =>
    permission.name.toLowerCase().includes(searchText.toLowerCase()) ||
    permission.code.toLowerCase().includes(searchText.toLowerCase()) ||
    permission.module.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '权限名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '权限代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '所属模块',
      dataIndex: 'module',
      key: 'module',
      render: (module: string) => (
        <Tag color="purple">{module}</Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Permission) => (
        <Space size="middle">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个权限吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>权限管理</Title>
      
      <Card>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Input
              placeholder="搜索权限名称、代码或模块"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col span={16} style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加权限
            </Button>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={filteredPermissions}
          loading={loading}
          rowKey="id"
          pagination={{
            total: filteredPermissions.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} 共 ${total} 条`,
          }}
        />
      </Card>

      <Modal
        title={editingPermission ? '编辑权限' : '添加权限'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="权限名称"
                rules={[
                  { required: true, message: '请输入权限名称' },
                  { min: 2, message: '权限名称至少2个字符' },
                ]}
              >
                <Input placeholder="请输入权限名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="权限代码"
                rules={[
                  { required: true, message: '请输入权限代码' },
                  { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '权限代码只能包含字母、数字和下划线，且不能以数字开头' },
                ]}
              >
                <Input placeholder="请输入权限代码" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="module"
            label="所属模块"
            rules={[{ required: true, message: '请选择所属模块' }]}
          >
            <Select placeholder="请选择所属模块">
              <Option value="用户管理">用户管理</Option>
              <Option value="权限管理">权限管理</Option>
              <Option value="系统管理">系统管理</Option>
              <Option value="内容管理">内容管理</Option>
              <Option value="数据统计">数据统计</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="权限描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入权限描述"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Permissions; 