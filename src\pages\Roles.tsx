import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Tag,
  Popconfirm,
  message,
  Card,
  Row,
  Col,
  Typography,
  Transfer,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { Role, Permission } from '../types';
import { generateMockRoles, generateMockPermissions, formatDate } from '../utils';

const { Title } = Typography;
const { TextArea } = Input;

const Roles: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [form] = Form.useForm();

  useEffect(() => {
    loadRoles();
    loadPermissions();
  }, []);

  const loadRoles = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const mockRoles = generateMockRoles(10);
      setRoles(mockRoles);
    } catch (error) {
      message.error('加载角色数据失败');
    } finally {
      setLoading(false);
    }
  };

  const loadPermissions = async () => {
    try {
      const mockPermissions = generateMockPermissions(20);
      setPermissions(mockPermissions);
    } catch (error) {
      message.error('加载权限数据失败');
    }
  };

  const handleAdd = () => {
    setEditingRole(null);
    setModalVisible(true);
    setSelectedPermissions([]);
    form.resetFields();
  };

  const handleEdit = (role: Role) => {
    setEditingRole(role);
    setModalVisible(true);
    setSelectedPermissions(role.permissions.map(p => p.id.toString()));
    form.setFieldsValue({
      name: role.name,
      code: role.code,
      description: role.description,
    });
  };

  const handleDelete = async (id: number) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      setRoles(roles.filter(role => role.id !== id));
      message.success('删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      const selectedPerms = permissions.filter(p => 
        selectedPermissions.includes(p.id.toString())
      );

      if (editingRole) {
        const updatedRole = {
          ...editingRole,
          ...values,
          permissions: selectedPerms,
          updatedAt: new Date().toISOString(),
        };
        setRoles(roles.map(role => 
          role.id === editingRole.id ? updatedRole : role
        ));
        message.success('更新成功');
      } else {
        const newRole: Role = {
          id: Date.now(),
          ...values,
          permissions: selectedPerms,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        setRoles([...roles, newRole]);
        message.success('添加成功');
      }
      setModalVisible(false);
      form.resetFields();
      setSelectedPermissions([]);
    } catch (error) {
      message.error('操作失败');
    }
  };

  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchText.toLowerCase()) ||
    role.code.toLowerCase().includes(searchText.toLowerCase())
  );

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '角色代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '权限数量',
      key: 'permissionCount',
      render: (_: any, record: Role) => (
        <Tag color="blue">{record.permissions.length}</Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Role) => (
        <Space size="middle">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个角色吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const transferDataSource = permissions.map(permission => ({
    key: permission.id.toString(),
    title: permission.name,
    description: permission.description,
  }));

  return (
    <div>
      <Title level={2}>角色管理</Title>
      
      <Card>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Input
              placeholder="搜索角色名称或代码"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col span={16} style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加角色
            </Button>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={filteredRoles}
          loading={loading}
          rowKey="id"
          pagination={{
            total: filteredRoles.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} 共 ${total} 条`,
          }}
        />
      </Card>

      <Modal
        title={editingRole ? '编辑角色' : '添加角色'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setSelectedPermissions([]);
        }}
        onOk={() => form.submit()}
        okText="确定"
        cancelText="取消"
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="角色名称"
                rules={[
                  { required: true, message: '请输入角色名称' },
                  { min: 2, message: '角色名称至少2个字符' },
                ]}
              >
                <Input placeholder="请输入角色名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="角色代码"
                rules={[
                  { required: true, message: '请输入角色代码' },
                  { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '角色代码只能包含字母、数字和下划线，且不能以数字开头' },
                ]}
              >
                <Input placeholder="请输入角色代码" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="角色描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入角色描述"
            />
          </Form.Item>

          <Form.Item label="权限配置">
            <Transfer
              dataSource={transferDataSource}
              targetKeys={selectedPermissions}
              onChange={(targetKeys) => setSelectedPermissions(targetKeys as string[])}
              render={item => item.title}
              titles={['可选权限', '已选权限']}
              showSearch
              filterOption={(inputValue, option) =>
                option.title.indexOf(inputValue) > -1
              }
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Roles; 