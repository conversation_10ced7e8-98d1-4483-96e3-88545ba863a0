// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  phone?: string;
  role: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface CreateUserData {
  username: string;
  email: string;
  phone?: string;
  role: string;
  password: string;
}

export interface UpdateUserData {
  username?: string;
  email?: string;
  phone?: string;
  role?: string;
  status?: 'active' | 'inactive';
}

// 权限相关类型
export interface Permission {
  id: number;
  name: string;
  code: string;
  description?: string;
  module: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePermissionData {
  name: string;
  code: string;
  description?: string;
  module: string;
}

export interface UpdatePermissionData {
  name?: string;
  code?: string;
  description?: string;
  module?: string;
}

// 角色相关类型
export interface Role {
  id: number;
  name: string;
  code: string;
  description?: string;
  permissions: Permission[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateRoleData {
  name: string;
  code: string;
  description?: string;
  permissionIds: number[];
}

export interface UpdateRoleData {
  name?: string;
  code?: string;
  description?: string;
  permissionIds?: number[];
}

// 系统配置类型
export interface SystemConfig {
  id: number;
  key: string;
  value: string;
  description?: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  createdAt: string;
  updatedAt: string;
}

export interface CreateSystemConfigData {
  key: string;
  value: string;
  description?: string;
  type: 'string' | 'number' | 'boolean' | 'json';
}

export interface UpdateSystemConfigData {
  key?: string;
  value?: string;
  description?: string;
  type?: 'string' | 'number' | 'boolean' | 'json';
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  code?: number;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    items: T[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  message?: string;
}

// 查询参数类型
export interface QueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  [key: string]: any;
}

// 菜单项类型
export interface MenuItem {
  key: string;
  label: string;
  icon?: any;
  children?: MenuItem[];
  path?: string;
} 