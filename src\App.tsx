import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import Layout from '@/components/Layout'
import Dashboard from '@/pages/Dashboard'
import Users from '@/pages/Users'
import Roles from '@/pages/Roles'
import Permissions from '@/pages/Permissions'
import System from '@/pages/System'
import './App.css'

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="users" element={<Users />} />
            <Route path="roles" element={<Roles />} />
            <Route path="permissions" element={<Permissions />} />
            <Route path="system" element={<System />} />
          </Route>
        </Routes>
      </Router>
    </ConfigProvider>
  )
}

export default App 