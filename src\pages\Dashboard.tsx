import React, { useEffect, useState } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Table,
  Tag,
  Progress,
  List,
  Avatar,
  Typography,
  Divider,
} from 'antd';
import {
  UserOutlined,
  SafetyOutlined,
  SettingOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { generateMockUsers, formatDate, getStatusColor, getStatusText } from '../utils';

const { Title } = Typography;

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalRoles: 0,
    totalPermissions: 0,
    totalConfigs: 0,
  });
  const [recentUsers, setRecentUsers] = useState<any[]>([]);
  const [systemHealth, setSystemHealth] = useState({
    status: 'healthy' as 'healthy' | 'warning' | 'error',
    uptime: '0天',
    memory: 0,
    cpu: 0,
  });

  useEffect(() => {
    // 模拟加载数据
    const loadData = async () => {
      // 模拟统计数据
      setStats({
        totalUsers: 1234,
        totalRoles: 8,
        totalPermissions: 45,
        totalConfigs: 23,
      });

      // 模拟最近用户
      const mockUsers = generateMockUsers(5);
      setRecentUsers(mockUsers);

      // 模拟系统健康状态
      setSystemHealth({
        status: 'healthy',
        uptime: '15天',
        memory: 65,
        cpu: 23,
      });
    };

    loadData();
  }, []);

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'green';
      case 'warning':
        return 'orange';
      case 'error':
        return 'red';
      default:
        return 'gray';
    }
  };

  const getHealthText = (status: string) => {
    switch (status) {
      case 'healthy':
        return '健康';
      case 'warning':
        return '警告';
      case 'error':
        return '错误';
      default:
        return '未知';
    }
  };

  const recentUserColumns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => (
        <Tag color="blue">{role}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: 'active' | 'inactive') => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => formatDate(date),
    },
  ];

  return (
    <div>
      <Title level={2}>仪表盘</Title>
      
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="角色数量"
              value={stats.totalRoles}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="权限数量"
              value={stats.totalPermissions}
              prefix={<SafetyOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="配置项数量"
              value={stats.totalConfigs}
              prefix={<SettingOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* 最近用户 */}
        <Col span={16}>
          <Card title="最近注册用户" style={{ marginBottom: 24 }}>
            <Table
              dataSource={recentUsers}
              columns={recentUserColumns}
              pagination={false}
              size="small"
              rowKey="id"
            />
          </Card>
        </Col>

        {/* 系统状态 */}
        <Col span={8}>
          <Card title="系统状态" style={{ marginBottom: 24 }}>
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>系统状态</span>
                <Tag color={getHealthColor(systemHealth.status)}>
                  {getHealthText(systemHealth.status)}
                </Tag>
              </div>
            </div>
            
            <Divider />
            
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>运行时间</span>
                <span>{systemHealth.uptime}</span>
              </div>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <div style={{ marginBottom: 8 }}>内存使用率</div>
              <Progress percent={systemHealth.memory} status="active" />
            </div>
            
            <div>
              <div style={{ marginBottom: 8 }}>CPU使用率</div>
              <Progress percent={systemHealth.cpu} status="active" />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作">
        <List
          grid={{ gutter: 16, column: 4 }}
          dataSource={[
            { title: '添加用户', description: '快速添加新用户', icon: <UserOutlined /> },
            { title: '创建角色', description: '创建新的用户角色', icon: <TeamOutlined /> },
            { title: '权限设置', description: '配置系统权限', icon: <SafetyOutlined /> },
            { title: '系统配置', description: '修改系统设置', icon: <SettingOutlined /> },
          ]}
          renderItem={(item) => (
            <List.Item>
              <Card
                hoverable
                style={{ textAlign: 'center' }}
                bodyStyle={{ padding: 16 }}
              >
                <Avatar
                  size={48}
                  icon={item.icon}
                  style={{ backgroundColor: '#1890ff', marginBottom: 12 }}
                />
                <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{item.title}</div>
                <div style={{ color: '#666', fontSize: 12 }}>{item.description}</div>
              </Card>
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default Dashboard; 