import { create } from 'zustand';
import { User, Permission, Role, SystemConfig } from '../types';

// 用户状态管理
interface UserState {
  users: User[];
  loading: boolean;
  selectedUser: User | null;
  setUsers: (users: User[]) => void;
  setLoading: (loading: boolean) => void;
  setSelectedUser: (user: User | null) => void;
  addUser: (user: User) => void;
  updateUser: (id: number, user: Partial<User>) => void;
  deleteUser: (id: number) => void;
}

export const useUserStore = create<UserState>((set) => ({
  users: [],
  loading: false,
  selectedUser: null,
  setUsers: (users) => set({ users }),
  setLoading: (loading) => set({ loading }),
  setSelectedUser: (selectedUser) => set({ selectedUser }),
  addUser: (user) => set((state) => ({ users: [...state.users, user] })),
  updateUser: (id, updatedUser) =>
    set((state) => ({
      users: state.users.map((user) =>
        user.id === id ? { ...user, ...updatedUser } : user
      ),
    })),
  deleteUser: (id) =>
    set((state) => ({
      users: state.users.filter((user) => user.id !== id),
    })),
}));

// 权限状态管理
interface PermissionState {
  permissions: Permission[];
  loading: boolean;
  selectedPermission: Permission | null;
  setPermissions: (permissions: Permission[]) => void;
  setLoading: (loading: boolean) => void;
  setSelectedPermission: (permission: Permission | null) => void;
  addPermission: (permission: Permission) => void;
  updatePermission: (id: number, permission: Partial<Permission>) => void;
  deletePermission: (id: number) => void;
}

export const usePermissionStore = create<PermissionState>((set) => ({
  permissions: [],
  loading: false,
  selectedPermission: null,
  setPermissions: (permissions) => set({ permissions }),
  setLoading: (loading) => set({ loading }),
  setSelectedPermission: (selectedPermission) => set({ selectedPermission }),
  addPermission: (permission) =>
    set((state) => ({ permissions: [...state.permissions, permission] })),
  updatePermission: (id, updatedPermission) =>
    set((state) => ({
      permissions: state.permissions.map((permission) =>
        permission.id === id ? { ...permission, ...updatedPermission } : permission
      ),
    })),
  deletePermission: (id) =>
    set((state) => ({
      permissions: state.permissions.filter((permission) => permission.id !== id),
    })),
}));

// 角色状态管理
interface RoleState {
  roles: Role[];
  loading: boolean;
  selectedRole: Role | null;
  setRoles: (roles: Role[]) => void;
  setLoading: (loading: boolean) => void;
  setSelectedRole: (role: Role | null) => void;
  addRole: (role: Role) => void;
  updateRole: (id: number, role: Partial<Role>) => void;
  deleteRole: (id: number) => void;
}

export const useRoleStore = create<RoleState>((set) => ({
  roles: [],
  loading: false,
  selectedRole: null,
  setRoles: (roles) => set({ roles }),
  setLoading: (loading) => set({ loading }),
  setSelectedRole: (selectedRole) => set({ selectedRole }),
  addRole: (role) => set((state) => ({ roles: [...state.roles, role] })),
  updateRole: (id, updatedRole) =>
    set((state) => ({
      roles: state.roles.map((role) =>
        role.id === id ? { ...role, ...updatedRole } : role
      ),
    })),
  deleteRole: (id) =>
    set((state) => ({
      roles: state.roles.filter((role) => role.id !== id),
    })),
}));

// 系统配置状态管理
interface SystemConfigState {
  configs: SystemConfig[];
  loading: boolean;
  selectedConfig: SystemConfig | null;
  setConfigs: (configs: SystemConfig[]) => void;
  setLoading: (loading: boolean) => void;
  setSelectedConfig: (config: SystemConfig | null) => void;
  addConfig: (config: SystemConfig) => void;
  updateConfig: (id: number, config: Partial<SystemConfig>) => void;
  deleteConfig: (id: number) => void;
}

export const useSystemConfigStore = create<SystemConfigState>((set) => ({
  configs: [],
  loading: false,
  selectedConfig: null,
  setConfigs: (configs) => set({ configs }),
  setLoading: (loading) => set({ loading }),
  setSelectedConfig: (selectedConfig) => set({ selectedConfig }),
  addConfig: (config) =>
    set((state) => ({ configs: [...state.configs, config] })),
  updateConfig: (id, updatedConfig) =>
    set((state) => ({
      configs: state.configs.map((config) =>
        config.id === id ? { ...config, ...updatedConfig } : config
      ),
    })),
  deleteConfig: (id) =>
    set((state) => ({
      configs: state.configs.filter((config) => config.id !== id),
    })),
}));

// 应用全局状态
interface AppState {
  collapsed: boolean;
  theme: 'light' | 'dark';
  currentUser: User | null;
  setCollapsed: (collapsed: boolean) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  setCurrentUser: (user: User | null) => void;
}

export const useAppStore = create<AppState>((set) => ({
  collapsed: false,
  theme: 'light',
  currentUser: null,
  setCollapsed: (collapsed) => set({ collapsed }),
  setTheme: (theme) => set({ theme }),
  setCurrentUser: (currentUser) => set({ currentUser }),
})); 