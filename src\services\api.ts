import {
  User,
  CreateUserData,
  UpdateUserData,
  Permission,
  CreatePermissionData,
  UpdatePermissionData,
  Role,
  CreateRoleData,
  UpdateRoleData,
  SystemConfig,
  CreateSystemConfigData,
  UpdateSystemConfigData,
  ApiResponse,
  PaginatedResponse,
  QueryParams,
} from '../types';

const API_BASE_URL = 'http://localhost:3001/api';

class ApiService {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // 用户相关API
  async getUsers(params?: QueryParams): Promise<PaginatedResponse<User>> {
    const queryString = params ? `?${new URLSearchParams(params as any).toString()}` : '';
    return this.request<PaginatedResponse<User>>(`/users${queryString}`);
  }

  async getUserById(id: number): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>(`/users/${id}`);
  }

  async createUser(data: CreateUserData): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>('/users', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateUser(id: number, data: UpdateUserData): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteUser(id: number): Promise<ApiResponse<void>> {
    return this.request<ApiResponse<void>>(`/users/${id}`, {
      method: 'DELETE',
    });
  }

  // 权限相关API
  async getPermissions(params?: QueryParams): Promise<PaginatedResponse<Permission>> {
    const queryString = params ? `?${new URLSearchParams(params as any).toString()}` : '';
    return this.request<PaginatedResponse<Permission>>(`/permissions${queryString}`);
  }

  async getPermissionById(id: number): Promise<ApiResponse<Permission>> {
    return this.request<ApiResponse<Permission>>(`/permissions/${id}`);
  }

  async createPermission(data: CreatePermissionData): Promise<ApiResponse<Permission>> {
    return this.request<ApiResponse<Permission>>('/permissions', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updatePermission(id: number, data: UpdatePermissionData): Promise<ApiResponse<Permission>> {
    return this.request<ApiResponse<Permission>>(`/permissions/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deletePermission(id: number): Promise<ApiResponse<void>> {
    return this.request<ApiResponse<void>>(`/permissions/${id}`, {
      method: 'DELETE',
    });
  }

  // 角色相关API
  async getRoles(params?: QueryParams): Promise<PaginatedResponse<Role>> {
    const queryString = params ? `?${new URLSearchParams(params as any).toString()}` : '';
    return this.request<PaginatedResponse<Role>>(`/roles${queryString}`);
  }

  async getRoleById(id: number): Promise<ApiResponse<Role>> {
    return this.request<ApiResponse<Role>>(`/roles/${id}`);
  }

  async createRole(data: CreateRoleData): Promise<ApiResponse<Role>> {
    return this.request<ApiResponse<Role>>('/roles', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateRole(id: number, data: UpdateRoleData): Promise<ApiResponse<Role>> {
    return this.request<ApiResponse<Role>>(`/roles/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteRole(id: number): Promise<ApiResponse<void>> {
    return this.request<ApiResponse<void>>(`/roles/${id}`, {
      method: 'DELETE',
    });
  }

  // 系统配置相关API
  async getSystemConfigs(params?: QueryParams): Promise<PaginatedResponse<SystemConfig>> {
    const queryString = params ? `?${new URLSearchParams(params as any).toString()}` : '';
    return this.request<PaginatedResponse<SystemConfig>>(`/system-configs${queryString}`);
  }

  async getSystemConfigById(id: number): Promise<ApiResponse<SystemConfig>> {
    return this.request<ApiResponse<SystemConfig>>(`/system-configs/${id}`);
  }

  async createSystemConfig(data: CreateSystemConfigData): Promise<ApiResponse<SystemConfig>> {
    return this.request<ApiResponse<SystemConfig>>('/system-configs', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateSystemConfig(id: number, data: UpdateSystemConfigData): Promise<ApiResponse<SystemConfig>> {
    return this.request<ApiResponse<SystemConfig>>(`/system-configs/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteSystemConfig(id: number): Promise<ApiResponse<void>> {
    return this.request<ApiResponse<void>>(`/system-configs/${id}`, {
      method: 'DELETE',
    });
  }

  // 统计数据API
  async getDashboardStats(): Promise<ApiResponse<{
    totalUsers: number;
    totalRoles: number;
    totalPermissions: number;
    totalConfigs: number;
    recentUsers: User[];
    systemHealth: {
      status: 'healthy' | 'warning' | 'error';
      uptime: string;
      memory: number;
      cpu: number;
    };
  }>> {
    return this.request<ApiResponse<any>>('/dashboard/stats');
  }
}

export const apiService = new ApiService(); 