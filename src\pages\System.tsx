import React, { useEffect, useState } from 'react';
import {
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  Popconfirm,
  message,
  Card,
  Row,
  Col,
  Typography,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { SystemConfig } from '../types';
import { generateMockSystemConfigs, formatDate } from '../utils';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const System: React.FC = () => {
  const [configs, setConfigs] = useState<SystemConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<SystemConfig | null>(null);
  const [searchText, setSearchText] = useState('');
  const [form] = Form.useForm();

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    setLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const mockConfigs = generateMockSystemConfigs(15);
      setConfigs(mockConfigs);
    } catch (error) {
      message.error('加载配置数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingConfig(null);
    setModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (config: SystemConfig) => {
    setEditingConfig(config);
    setModalVisible(true);
    form.setFieldsValue(config);
  };

  const handleDelete = async (id: number) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      setConfigs(configs.filter(config => config.id !== id));
      message.success('删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingConfig) {
        const updatedConfig = {
          ...editingConfig,
          ...values,
          updatedAt: new Date().toISOString(),
        };
        setConfigs(configs.map(config => 
          config.id === editingConfig.id ? updatedConfig : config
        ));
        message.success('更新成功');
      } else {
        const newConfig: SystemConfig = {
          id: Date.now(),
          ...values,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        setConfigs([...configs, newConfig]);
        message.success('添加成功');
      }
      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const filteredConfigs = configs.filter(config =>
    config.key.toLowerCase().includes(searchText.toLowerCase()) ||
    (config.description && config.description.toLowerCase().includes(searchText.toLowerCase()))
  );

  const getTypeColor = (type: string) => {
    const colors = {
      string: 'blue',
      number: 'green',
      boolean: 'orange',
      json: 'purple',
    };
    return colors[type as keyof typeof colors] || 'default';
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '配置键',
      dataIndex: 'key',
      key: 'key',
    },
    {
      title: '配置值',
      dataIndex: 'value',
      key: 'value',
      ellipsis: true,
      width: 200,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={getTypeColor(type)}>{type}</Tag>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => formatDate(date),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: SystemConfig) => (
        <Space size="middle">
          <Button
            type="primary"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个配置吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="primary"
              danger
              size="small"
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>系统管理</Title>
      
      <Card>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Input
              placeholder="搜索配置键或描述"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
          </Col>
          <Col span={16} style={{ textAlign: 'right' }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加配置
            </Button>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={filteredConfigs}
          loading={loading}
          rowKey="id"
          pagination={{
            total: filteredConfigs.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} 共 ${total} 条`,
          }}
        />
      </Card>

      <Modal
        title={editingConfig ? '编辑配置' : '添加配置'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="key"
                label="配置键"
                rules={[
                  { required: true, message: '请输入配置键' },
                  { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '配置键只能包含字母、数字和下划线，且不能以数字开头' },
                ]}
              >
                <Input placeholder="请输入配置键" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="type"
                label="数据类型"
                rules={[{ required: true, message: '请选择数据类型' }]}
              >
                <Select placeholder="请选择数据类型">
                  <Option value="string">字符串</Option>
                  <Option value="number">数字</Option>
                  <Option value="boolean">布尔值</Option>
                  <Option value="json">JSON</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="value"
            label="配置值"
            rules={[{ required: true, message: '请输入配置值' }]}
          >
            <TextArea
              rows={3}
              placeholder="请输入配置值"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="配置描述"
          >
            <TextArea
              rows={2}
              placeholder="请输入配置描述"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default System; 